import { create } from 'zustand';

export interface FinancialData {
  revenue: number;
  profit: number;
  expenses: number;
  assets: number;
  liabilities: number;
  cashFlow: number;
  period: string;
  year: number;
}

export interface Report {
  id: string;
  title: string;
  type: 'financial_health' | 'market_analysis' | 'growth_potential';
  status: 'generating' | 'completed' | 'failed';
  score?: number;
  insights: string[];
  recommendations: string[];
  createdAt: string;
  updatedAt: string;
}

export interface ActionItem {
  id: string;
  title: string;
  description: string;
  priority: 'high' | 'medium' | 'low';
  category: 'financial' | 'operational' | 'compliance' | 'growth';
  completed: boolean;
  dueDate?: string;
  impact: number; // 1-10 scale
}

export interface UploadedDocument {
  id: string;
  name: string;
  type: string;
  size: number;
  uploadedAt: string;
  status: 'processing' | 'completed' | 'failed';
  category: 'financial_statements' | 'tax_returns' | 'bank_statements' | 'other';
}

interface SMEState {
  // Financial data
  financialData: FinancialData | null;
  healthScore: number;
  scoreBreakdown: {
    financial: number;
    operational: number;
    market: number;
    growth: number;
  };
  
  // File uploads
  uploadProgress: number;
  uploadedDocuments: UploadedDocument[];
  
  // Reports and analysis
  reports: Report[];
  currentReport: Report | null;
  
  // Action items and recommendations
  actionItems: ActionItem[];
  completedActions: number;
  
  // UI state
  isLoading: boolean;
  error: string | null;
  
  // Actions
  setFinancialData: (data: FinancialData) => void;
  updateHealthScore: (score: number, breakdown?: Partial<SMEState['scoreBreakdown']>) => void;
  setUploadProgress: (progress: number) => void;
  addDocument: (document: UploadedDocument) => void;
  updateDocumentStatus: (id: string, status: UploadedDocument['status']) => void;
  addReport: (report: Report) => void;
  updateReport: (id: string, updates: Partial<Report>) => void;
  setCurrentReport: (report: Report | null) => void;
  addActionItem: (item: ActionItem) => void;
  toggleActionItem: (id: string) => void;
  updateActionItem: (id: string, updates: Partial<ActionItem>) => void;
  setLoading: (loading: boolean) => void;
  setError: (error: string | null) => void;
  clearError: () => void;
}

export const useSMEStore = create<SMEState>((set, get) => ({
  // Initial state
  financialData: null,
  healthScore: 0,
  scoreBreakdown: {
    financial: 0,
    operational: 0,
    market: 0,
    growth: 0,
  },
  uploadProgress: 0,
  uploadedDocuments: [],
  reports: [],
  currentReport: null,
  actionItems: [],
  completedActions: 0,
  isLoading: false,
  error: null,

  // Actions
  setFinancialData: (data: FinancialData) => {
    set({ financialData: data });
  },

  updateHealthScore: (score: number, breakdown?: Partial<SMEState['scoreBreakdown']>) => {
    set((state) => ({
      healthScore: score,
      scoreBreakdown: breakdown ? { ...state.scoreBreakdown, ...breakdown } : state.scoreBreakdown,
    }));
  },

  setUploadProgress: (progress: number) => {
    set({ uploadProgress: progress });
  },

  addDocument: (document: UploadedDocument) => {
    set((state) => ({
      uploadedDocuments: [...state.uploadedDocuments, document],
    }));
  },

  updateDocumentStatus: (id: string, status: UploadedDocument['status']) => {
    set((state) => ({
      uploadedDocuments: state.uploadedDocuments.map((doc) =>
        doc.id === id ? { ...doc, status } : doc
      ),
    }));
  },

  addReport: (report: Report) => {
    set((state) => ({
      reports: [...state.reports, report],
    }));
  },

  updateReport: (id: string, updates: Partial<Report>) => {
    set((state) => ({
      reports: state.reports.map((report) =>
        report.id === id ? { ...report, ...updates } : report
      ),
    }));
  },

  setCurrentReport: (report: Report | null) => {
    set({ currentReport: report });
  },

  addActionItem: (item: ActionItem) => {
    set((state) => ({
      actionItems: [...state.actionItems, item],
    }));
  },

  toggleActionItem: (id: string) => {
    set((state) => {
      const updatedItems = state.actionItems.map((item) =>
        item.id === id ? { ...item, completed: !item.completed } : item
      );
      const completedCount = updatedItems.filter((item) => item.completed).length;
      
      return {
        actionItems: updatedItems,
        completedActions: completedCount,
      };
    });
  },

  updateActionItem: (id: string, updates: Partial<ActionItem>) => {
    set((state) => ({
      actionItems: state.actionItems.map((item) =>
        item.id === id ? { ...item, ...updates } : item
      ),
    }));
  },

  setLoading: (loading: boolean) => {
    set({ isLoading: loading });
  },

  setError: (error: string | null) => {
    set({ error });
  },

  clearError: () => {
    set({ error: null });
  },
}));
