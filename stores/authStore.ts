import { create } from 'zustand';
import { persist } from 'zustand/middleware';

export type UserRole = 'sme' | 'investor' | 'consultant';

export interface User {
  id: string;
  email: string;
  name: string;
  role: UserRole;
  companyName?: string;
  profileComplete: boolean;
  createdAt: string;
}

export interface LoginCredentials {
  email: string;
  password: string;
}

export interface SignUpData {
  email: string;
  password: string;
  name: string;
  role: UserRole;
  companyName?: string;
}

interface AuthState {
  user: User | null;
  role: UserRole | null;
  isAuthenticated: boolean;
  isLoading: boolean;
  error: string | null;
  
  // Actions
  login: (credentials: LoginCredentials) => Promise<void>;
  signup: (data: SignUpData) => Promise<void>;
  logout: () => void;
  updateProfile: (updates: Partial<User>) => void;
  clearError: () => void;
  setLoading: (loading: boolean) => void;
}

export const useAuthStore = create<AuthState>()(
  persist(
    (set, get) => ({
      // Auto-login for development
      user: {
        id: "dev-user-1",
        email: "<EMAIL>",
        name: "Demo Investor",
        role: "investor" as User<PERSON>ole,
        companyName: "Demo Investment Firm",
        profileComplete: true,
        createdAt: new Date().toISOString(),
      },
      role: "investor" as UserRole,
      isAuthenticated: true,
      isLoading: false,
      error: null,

      login: async (credentials: LoginCredentials) => {
        console.log('Auth store login called with:', credentials);
        set({ isLoading: true, error: null });

        try {
          // TODO: Replace with actual API call
          // Simulate API call
          console.log('Simulating API call...');
          await new Promise(resolve => setTimeout(resolve, 1000));

          // Determine role based on email
          let role: UserRole = 'sme';
          let name = 'John Doe';
          let companyName = 'Tech Startup Inc.';

          if (credentials.email.includes('investor')) {
            role = 'investor';
            name = 'Jane Smith';
            companyName = 'Investment Partners LLC';
          } else if (credentials.email.includes('consultant')) {
            role = 'consultant';
            name = 'Mike Johnson';
            companyName = 'Business Consulting Group';
          } else if (credentials.email.includes('sme')) {
            role = 'sme';
            name = 'Sarah Wilson';
            companyName = 'Growing Business Ltd.';
          }

          // Mock user data - replace with actual API response
          const mockUser: User = {
            id: Date.now().toString(),
            email: credentials.email,
            name: name,
            role: role,
            companyName: companyName,
            profileComplete: true,
            createdAt: new Date().toISOString(),
          };

          console.log('Setting user in auth store:', mockUser);
          set({
            user: mockUser,
            role: mockUser.role,
            isAuthenticated: true,
            isLoading: false,
            error: null,
          });
          console.log('Auth store updated successfully');
        } catch (error) {
          set({
            error: error instanceof Error ? error.message : 'Login failed',
            isLoading: false,
          });
        }
      },

      signup: async (data: SignUpData) => {
        set({ isLoading: true, error: null });
        
        try {
          // TODO: Replace with actual API call
          // Simulate API call
          await new Promise(resolve => setTimeout(resolve, 1000));
          
          // Mock user data - replace with actual API response
          const mockUser: User = {
            id: Date.now().toString(),
            email: data.email,
            name: data.name,
            role: data.role,
            companyName: data.companyName,
            profileComplete: false,
            createdAt: new Date().toISOString(),
          };

          set({
            user: mockUser,
            role: mockUser.role,
            isAuthenticated: true,
            isLoading: false,
            error: null,
          });
        } catch (error) {
          set({
            error: error instanceof Error ? error.message : 'Signup failed',
            isLoading: false,
          });
        }
      },

      logout: () => {
        set({
          user: null,
          role: null,
          isAuthenticated: false,
          error: null,
        });
      },

      updateProfile: (updates: Partial<User>) => {
        const { user } = get();
        if (user) {
          set({
            user: { ...user, ...updates },
          });
        }
      },

      clearError: () => {
        set({ error: null });
      },

      setLoading: (loading: boolean) => {
        set({ isLoading: loading });
      },
    }),
    {
      name: 'auth-storage',
      partialize: (state) => ({
        user: state.user,
        role: state.role,
        isAuthenticated: state.isAuthenticated,
      }),
    }
  )
);
