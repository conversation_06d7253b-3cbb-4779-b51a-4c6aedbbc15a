// Server component for static generation
import DealDetailClient from './DealDetailClient';

// Generate static params for static export
export async function generateStaticParams() {
  // Generate some sample deal IDs for static export
  return [
    { id: '1' },
    { id: '2' },
    { id: '3' },
    { id: '4' },
    { id: '5' },
  ];
}

// Server component wrapper
export default function DealDetailPage({ params }: { params: { id: string } }) {
  return <DealDetailClient />;
}
