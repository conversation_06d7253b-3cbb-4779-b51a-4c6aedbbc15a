"use client";

import { Badge } from "@/components/ui/badge";
import { But<PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { useAuthStore } from "@/stores/authStore";
import { motion } from "framer-motion";
import {
    ArrowLeft,
    BarChart3,
    Calendar,
    Download,
    Eye,
    FileText,
    Target,
    TrendingDown,
    TrendingUp,
    Users
} from "lucide-react";
import Link from "next/link";

export default function ReportsPage() {
  const { user } = useAuthStore();

  const reports = [
    {
      id: 1,
      title: "Financial Health Score Report",
      description: "Comprehensive analysis of your business financial health",
      score: 78,
      trend: "up",
      lastUpdated: "2 days ago",
      type: "financial",
      icon: BarChart3
    },
    {
      id: 2,
      title: "Investor Readiness Report",
      description: "Assessment of your readiness to attract investors",
      score: 85,
      trend: "up",
      lastUpdated: "1 week ago",
      type: "investor",
      icon: Users
    },
    {
      id: 3,
      title: "Growth Potential Analysis",
      description: "Market opportunities and growth projections",
      score: 72,
      trend: "down",
      lastUpdated: "3 days ago",
      type: "growth",
      icon: Target
    },
    {
      id: 4,
      title: "Monthly Performance Report",
      description: "Key metrics and performance indicators for this month",
      score: 91,
      trend: "up",
      lastUpdated: "1 day ago",
      type: "performance",
      icon: TrendingUp
    }
  ];

  const keyMetrics = [
    { label: "Revenue Growth", value: "+23%", trend: "up", color: "emerald" },
    { label: "Profit Margin", value: "18.5%", trend: "up", color: "blue" },
    { label: "Cash Flow", value: "$45K", trend: "down", color: "amber" },
    { label: "Investor Interest", value: "15 views", trend: "up", color: "purple" }
  ];

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-900 via-slate-800 to-slate-900">
      {/* Header */}
      <header className="border-b border-slate-700/50 bg-slate-900/80 backdrop-blur-sm sticky top-0 z-50">
        <div className="container mx-auto px-4 py-4 flex items-center justify-between">
          <div className="flex items-center space-x-4">
            <Link href="/sme/dashboard" className="flex items-center space-x-3">
              <ArrowLeft className="w-5 h-5 text-slate-400 hover:text-white transition-colors" />
              <span className="text-slate-400 hover:text-white transition-colors">Back to Dashboard</span>
            </Link>
          </div>
          <div className="flex items-center space-x-3">
            <div className="w-8 h-8 bg-blue-600 rounded-lg flex items-center justify-center">
              <span className="text-white font-bold text-sm">CF</span>
            </div>
            <span className="text-xl font-bold text-white">CFOx</span>
          </div>
        </div>
      </header>

      <div className="container mx-auto px-4 py-8">
        {/* Page Header */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6 }}
          className="mb-8"
        >
          <div className="flex items-center justify-between mb-4">
            <div>
              <h1 className="text-3xl font-bold text-white mb-2">Business Reports</h1>
              <p className="text-slate-400">Comprehensive insights and analytics for your business</p>
            </div>
            <Button className="bg-blue-600 hover:bg-blue-700">
              <Download className="w-4 h-4 mr-2" />
              Export All Reports
            </Button>
          </div>
        </motion.div>

        {/* Key Metrics */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6, delay: 0.2 }}
          className="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8"
        >
          {keyMetrics.map((metric, index) => (
            <Card key={index} className="bg-slate-800/50 border-slate-700/50">
              <CardContent className="p-6">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-slate-400 text-sm">{metric.label}</p>
                    <p className="text-2xl font-bold text-white mt-1">{metric.value}</p>
                  </div>
                  <div className={`p-2 rounded-lg bg-${metric.color}-500/20`}>
                    {metric.trend === 'up' ? (
                      <TrendingUp className={`w-5 h-5 text-${metric.color}-400`} />
                    ) : (
                      <TrendingDown className={`w-5 h-5 text-${metric.color}-400`} />
                    )}
                  </div>
                </div>
              </CardContent>
            </Card>
          ))}
        </motion.div>

        {/* Reports Grid */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6, delay: 0.4 }}
          className="grid grid-cols-1 md:grid-cols-2 gap-8"
        >
          {reports.map((report, index) => (
            <motion.div
              key={report.id}
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: 0.1 * index }}
            >
              <Card className="bg-slate-800/50 border-slate-700/50 hover:border-blue-500/50 transition-all duration-300 h-full">
                <CardHeader>
                  <div className="flex items-center justify-between mb-4">
                    <div className="flex items-center">
                      <div className="w-12 h-12 bg-blue-500/20 rounded-lg flex items-center justify-center mr-4">
                        <report.icon className="w-6 h-6 text-blue-400" />
                      </div>
                      <div>
                        <CardTitle className="text-white text-lg">{report.title}</CardTitle>
                        <div className="flex items-center mt-2">
                          <Badge className={`mr-2 ${
                            report.score >= 80 ? 'bg-emerald-500/20 text-emerald-400 border-emerald-500/30' :
                            report.score >= 60 ? 'bg-amber-500/20 text-amber-400 border-amber-500/30' :
                            'bg-red-500/20 text-red-400 border-red-500/30'
                          }`}>
                            Score: {report.score}
                          </Badge>
                          {report.trend === 'up' ? (
                            <TrendingUp className="w-4 h-4 text-emerald-400" />
                          ) : (
                            <TrendingDown className="w-4 h-4 text-red-400" />
                          )}
                        </div>
                      </div>
                    </div>
                  </div>
                </CardHeader>
                <CardContent>
                  <CardDescription className="text-slate-300 mb-4">
                    {report.description}
                  </CardDescription>
                  <div className="flex items-center justify-between">
                    <div className="flex items-center text-slate-400 text-sm">
                      <Calendar className="w-4 h-4 mr-1" />
                      Updated {report.lastUpdated}
                    </div>
                    <div className="flex space-x-2">
                      <Button variant="outline" size="sm" className="border-slate-600 text-slate-300 hover:bg-slate-700">
                        <Eye className="w-4 h-4 mr-1" />
                        View
                      </Button>
                      <Button variant="outline" size="sm" className="border-slate-600 text-slate-300 hover:bg-slate-700">
                        <Download className="w-4 h-4 mr-1" />
                        Export
                      </Button>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </motion.div>
          ))}
        </motion.div>

        {/* Report Generation */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6, delay: 0.6 }}
          className="mt-8"
        >
          <Card className="bg-gradient-to-r from-blue-900/50 to-purple-900/50 border-blue-500/30">
            <CardContent className="p-8">
              <div className="text-center">
                <FileText className="w-12 h-12 text-blue-400 mx-auto mb-4" />
                <h3 className="text-2xl font-bold text-white mb-2">Generate Custom Report</h3>
                <p className="text-slate-300 mb-6 max-w-2xl mx-auto">
                  Need a specific analysis? Generate custom reports tailored to your business needs and investor requirements.
                </p>
                <div className="flex flex-col sm:flex-row gap-4 justify-center">
                  <Button className="bg-blue-600 hover:bg-blue-700">
                    <BarChart3 className="w-4 h-4 mr-2" />
                    Generate Financial Report
                  </Button>
                  <Button variant="outline" className="border-blue-500 text-blue-400 hover:bg-blue-500/10">
                    <Users className="w-4 h-4 mr-2" />
                    Create Investor Deck
                  </Button>
                </div>
              </div>
            </CardContent>
          </Card>
        </motion.div>
      </div>
    </div>
  );
}
