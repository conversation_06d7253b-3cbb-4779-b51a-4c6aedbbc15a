"use client";

import AppHeader from "@/components/navigation/AppHeader";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Progress } from "@/components/ui/progress";
import { useAuthStore } from "@/stores/authStore";
import { motion } from "framer-motion";
import {
    ArrowRight,
    BarChart3,
    CheckCircle,
    Clock,
    FileText,
    Target,
    TrendingUp,
    Upload,
    Users
} from "lucide-react";
import Link from "next/link";
import { useRouter } from "next/navigation";

export default function SMEDashboard() {
  const router = useRouter();
  const { logout, user } = useAuthStore();

  const handleLogout = () => {
    logout();
    router.push('/');
  };
  // Mock data - in real app, this would come from API
  const financialScore = 78;
  const documentsUploaded = 8;
  const totalDocuments = 12;
  const investorInterest = 15;
  const profileCompletion = 85;

  const recentActivity = [
    { id: 1, type: "document", message: "Financial statements uploaded", time: "2 hours ago", status: "success" },
    { id: 2, type: "investor", message: "3 new investors viewed your profile", time: "5 hours ago", status: "info" },
    { id: 3, type: "score", message: "Financial health score improved by 5 points", time: "1 day ago", status: "success" },
    { id: 4, type: "meeting", message: "Advisor call scheduled for tomorrow", time: "2 days ago", status: "pending" }
  ];

  const actionItems = [
    { id: 1, task: "Upload bank statements (last 12 months)", priority: "high", completed: false },
    { id: 2, task: "Complete business profile information", priority: "medium", completed: false },
    { id: 3, task: "Schedule advisor consultation call", priority: "low", completed: true },
    { id: 4, task: "Review investor interest notifications", priority: "medium", completed: false }
  ];

  return (
    <div className="min-h-screen bg-white compact-layout">
      <AppHeader variant="sme" />

      <div className="container mx-auto px-3 py-4">
        {/* Welcome Section */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6 }}
          className="mb-4"
        >
          <div className="flex items-center justify-between mb-3">
            <div>
              <h1 className="text-2xl font-bold text-black mb-1">Welcome back, {user?.companyName || user?.name || 'User'}</h1>
              <p className="text-gray-600 text-sm">Here's your business performance overview</p>
            </div>
            <Badge variant="secondary" className="text-xs">
              Profile {profileCompletion}% Complete
            </Badge>
          </div>
        </motion.div>

        {/* Key Metrics Grid */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-3 mb-4">
          {/* Financial Health Score */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6, delay: 0.1 }}
          >
            <Card variant="compact" className="bg-white border-gray-200">
              <CardContent variant="compact">
                <div className="flex items-center justify-between mb-2">
                  <div className="w-8 h-8 bg-gray-100 rounded-md flex items-center justify-center">
                    <TrendingUp className="w-4 h-4 text-gray-700" />
                  </div>
                  <Badge variant="secondary" className="text-xs">+5 this month</Badge>
                </div>
                <div className="text-xl font-bold text-black mb-1">{financialScore}/100</div>
                <p className="text-gray-600 text-xs">Financial Health Score</p>
                <div className="mt-2">
                  <Progress value={financialScore} className="h-1.5" />
                </div>
              </CardContent>
            </Card>
          </motion.div>

          {/* Documents Status */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6, delay: 0.2 }}
          >
            <Card variant="compact" className="bg-white border-gray-200">
              <CardContent variant="compact">
                <div className="flex items-center justify-between mb-2">
                  <div className="w-8 h-8 bg-gray-100 rounded-md flex items-center justify-center">
                    <FileText className="w-4 h-4 text-gray-700" />
                  </div>
                  <Link href="/sme/dashboard/upload">
                    <Button size="compact" variant="ghost" className="text-gray-600 hover:text-gray-900">
                      <Upload className="w-3 h-3" />
                    </Button>
                  </Link>
                </div>
                <div className="text-xl font-bold text-black mb-1">{documentsUploaded}/{totalDocuments}</div>
                <p className="text-gray-600 text-xs">Documents Uploaded</p>
                <div className="mt-2">
                  <Progress value={(documentsUploaded / totalDocuments) * 100} className="h-1.5" />
                </div>
              </CardContent>
            </Card>
          </motion.div>

          {/* Investor Interest */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6, delay: 0.3 }}
          >
            <Card variant="compact" className="bg-white border-gray-200">
              <CardContent variant="compact">
                <div className="flex items-center justify-between mb-2">
                  <div className="w-8 h-8 bg-gray-100 rounded-md flex items-center justify-center">
                    <Users className="w-4 h-4 text-gray-700" />
                  </div>
                  <Badge variant="secondary" className="text-xs">+3 today</Badge>
                </div>
                <div className="text-xl font-bold text-black mb-1">{investorInterest}</div>
                <p className="text-gray-600 text-xs">Investor Views</p>
                <div className="mt-2 flex items-center text-gray-700 text-xs">
                  <TrendingUp className="w-3 h-3 mr-1" />
                  High interest level
                </div>
              </CardContent>
            </Card>
          </motion.div>

          {/* 10X Growth Status */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6, delay: 0.4 }}
          >
            <Card variant="compact" className="bg-white border-gray-200">
              <CardContent variant="compact">
                <div className="flex items-center justify-between mb-2">
                  <div className="w-8 h-8 bg-gray-100 rounded-md flex items-center justify-center">
                    <Target className="w-4 h-4 text-gray-700" />
                  </div>
                  <Badge variant="secondary" className="text-xs">Eligible</Badge>
                </div>
                <div className="text-lg font-bold text-black mb-1">10X Growth</div>
                <p className="text-gray-600 text-xs">Program Status</p>
                <div className="mt-2">
                  <Link href="/10x-growth-hack">
                    <Button size="compact" className="bg-black hover:bg-gray-800 text-white text-xs">
                      Apply Now
                    </Button>
                  </Link>
                </div>
              </CardContent>
            </Card>
          </motion.div>
        </div>

        {/* Main Content Grid */}
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-3">
          {/* Action Items */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6, delay: 0.5 }}
            className="lg:col-span-2"
          >
            <Card variant="compact" className="bg-white border-gray-200">
              <CardHeader variant="compact">
                <CardTitle className="text-black flex items-center text-base">
                  <CheckCircle className="w-4 h-4 mr-2 text-gray-700" />
                  Action Items
                </CardTitle>
                <CardDescription className="text-gray-600 text-xs">
                  Complete these tasks to improve your financial health score
                </CardDescription>
              </CardHeader>
              <CardContent variant="compact">
                <div className="space-y-2">
                  {actionItems.map((item) => (
                    <div
                      key={item.id}
                      className={`flex items-center justify-between p-2 rounded-md border ${
                        item.completed
                          ? "bg-green-50 border-green-200"
                          : "bg-gray-50 border-gray-200"
                      }`}
                    >
                      <div className="flex items-center space-x-2">
                        {item.completed ? (
                          <CheckCircle className="w-4 h-4 text-green-600" />
                        ) : (
                          <div className={`w-4 h-4 rounded-full border-2 ${
                            item.priority === "high" ? "border-red-500" :
                            item.priority === "medium" ? "border-amber-500" : "border-gray-400"
                          }`} />
                        )}
                        <div>
                          <p className={`text-sm font-medium ${item.completed ? "text-green-700" : "text-black"}`}>
                            {item.task}
                          </p>
                          <p className="text-xs text-gray-500 capitalize">
                            {item.priority} priority
                          </p>
                        </div>
                      </div>
                      {!item.completed && (
                        <Button size="compact" variant="ghost" className="text-gray-600 hover:text-gray-900">
                          <ArrowRight className="w-3 h-3" />
                        </Button>
                      )}
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          </motion.div>

          {/* Recent Activity */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6, delay: 0.6 }}
          >
            <Card variant="compact" className="bg-white border-gray-200">
              <CardHeader variant="compact">
                <CardTitle className="text-black flex items-center text-base">
                  <Clock className="w-4 h-4 mr-2 text-gray-700" />
                  Recent Activity
                </CardTitle>
              </CardHeader>
              <CardContent variant="compact">
                <div className="space-y-2">
                  {recentActivity.map((activity) => (
                    <div key={activity.id} className="flex items-start space-x-2">
                      <div className={`w-2 h-2 rounded-full mt-1.5 ${
                        activity.status === "success" ? "bg-green-500" :
                        activity.status === "info" ? "bg-blue-500" : "bg-amber-500"
                      }`} />
                      <div className="flex-1">
                        <p className="text-black text-xs">{activity.message}</p>
                        <p className="text-gray-500 text-xs">{activity.time}</p>
                      </div>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          </motion.div>
        </div>

        {/* Quick Actions */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6, delay: 0.7 }}
          className="mt-4"
        >
          <Card variant="compact" className="bg-white border-gray-200">
            <CardHeader variant="compact">
              <CardTitle className="text-black text-base">Quick Actions</CardTitle>
            </CardHeader>
            <CardContent variant="compact">
              <div className="grid grid-cols-1 md:grid-cols-4 gap-2">
                <Link href="/sme/dashboard/upload">
                  <Button size="compact" className="w-full bg-black hover:bg-gray-800 justify-start text-xs">
                    <Upload className="w-3 h-3 mr-1" />
                    Upload Documents
                  </Button>
                </Link>
                <Link href="/sme/dashboard/reports">
                  <Button size="compact" variant="outline" className="w-full border-gray-300 text-black hover:bg-gray-50 justify-start text-xs">
                    <BarChart3 className="w-3 h-3 mr-1" />
                    View Reports
                  </Button>
                </Link>
                <Link href="/sme/dashboard/advisor-call">
                  <Button size="compact" variant="outline" className="w-full border-gray-300 text-black hover:bg-gray-50 justify-start text-xs">
                    <Users className="w-3 h-3 mr-1" />
                    Schedule Call
                  </Button>
                </Link>
                <Link href="/10x-growth-hack">
                  <Button size="compact" variant="outline" className="w-full border-gray-300 text-black hover:bg-gray-50 justify-start text-xs">
                    <Target className="w-3 h-3 mr-1" />
                    10X Growth
                  </Button>
                </Link>
              </div>
            </CardContent>
          </Card>
        </motion.div>
      </div>
    </div>
  );
}
