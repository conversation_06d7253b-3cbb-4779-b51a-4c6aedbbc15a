@import "tailwindcss";
@import "tw-animate-css";

@theme inline {
  --color-background: var(--background);
  --color-foreground: var(--foreground);
  --font-sans: Inter, system-ui, sans-serif;
  --font-mono: JetBrains Mono, monospace;

  /* Grayscale Colors for Light Theme */
  --color-gray-50: #f9fafb;
  --color-gray-100: #f3f4f6;
  --color-gray-200: #e5e7eb;
  --color-gray-300: #d1d5db;
  --color-gray-400: #9ca3af;
  --color-gray-500: #6b7280;
  --color-gray-600: #4b5563;
  --color-gray-700: #374151;
  --color-gray-800: #1f2937;
  --color-gray-900: #111827;

  --color-sidebar-ring: var(--sidebar-ring);
  --color-sidebar-border: var(--sidebar-border);
  --color-sidebar-accent-foreground: var(--sidebar-accent-foreground);
  --color-sidebar-accent: var(--sidebar-accent);
  --color-sidebar-primary-foreground: var(--sidebar-primary-foreground);
  --color-sidebar-primary: var(--sidebar-primary);
  --color-sidebar-foreground: var(--sidebar-foreground);
  --color-sidebar: var(--sidebar);
  --color-chart-5: var(--chart-5);
  --color-chart-4: var(--chart-4);
  --color-chart-3: var(--chart-3);
  --color-chart-2: var(--chart-2);
  --color-chart-1: var(--chart-1);
  --color-ring: var(--ring);
  --color-input: var(--input);
  --color-border: var(--border);
  --color-destructive: var(--destructive);
  --color-accent-foreground: var(--accent-foreground);
  --color-accent: var(--accent);
  --color-muted-foreground: var(--muted-foreground);
  --color-muted: var(--muted);
  --color-secondary-foreground: var(--secondary-foreground);
  --color-secondary: var(--secondary);
  --color-primary-foreground: var(--primary-foreground);
  --color-primary: var(--primary);
  --color-popover-foreground: var(--popover-foreground);
  --color-popover: var(--popover);
  --color-card-foreground: var(--card-foreground);
  --color-card: var(--card);
  --radius-sm: calc(var(--radius) - 4px);
  --radius-md: calc(var(--radius) - 2px);
  --radius-lg: var(--radius);
  --radius-xl: calc(var(--radius) + 4px);
}

:root {
  --radius: 0.75rem;
  /* Light Theme Variables - Black and White Design */
  --background: #ffffff;
  --foreground: #000000;
  --card: #ffffff;
  --card-foreground: #000000;
  --popover: #ffffff;
  --popover-foreground: #000000;
  --primary: #000000;
  --primary-foreground: #ffffff;
  --secondary: #f9fafb;
  --secondary-foreground: #000000;
  --muted: #f3f4f6;
  --muted-foreground: #6b7280;
  --accent: #f9fafb;
  --accent-foreground: #000000;
  --destructive: #ef4444;
  --destructive-foreground: #ffffff;
  --border: #e5e7eb;
  --input: #e5e7eb;
  --ring: #000000;
  --chart-1: #000000;
  --chart-2: #374151;
  --chart-3: #6b7280;
  --chart-4: #9ca3af;
  --chart-5: #d1d5db;
  --sidebar: #ffffff;
  --sidebar-foreground: #000000;
  --sidebar-primary: #000000;
  --sidebar-primary-foreground: #ffffff;
  --sidebar-accent: #f9fafb;
  --sidebar-accent-foreground: #000000;
  --sidebar-border: #e5e7eb;
  --sidebar-ring: #000000;
}

/* Default Light Theme Styles */
.bg-gradient-to-br {
  background: linear-gradient(135deg, #f8fafc 0%, #ffffff 100%);
}

.from-slate-900,
.via-slate-800,
.to-slate-900 {
  background: #ffffff;
}

/* Compact Layout Overrides */
.compact-layout {
  /* Reduce container padding */
  --container-padding: 1rem;
  /* Reduce section spacing */
  --section-spacing: 1rem;
  /* Reduce card spacing */
  --card-spacing: 0.75rem;
}

/* Override excessive spacing in compact mode */
.compact-layout .container {
  @apply px-3;
}

.compact-layout .py-8 {
  @apply py-4;
}

.compact-layout .py-6 {
  @apply py-3;
}

.compact-layout .p-6 {
  @apply p-3;
}

.compact-layout .mb-8 {
  @apply mb-4;
}

.compact-layout .mb-6 {
  @apply mb-3;
}

.compact-layout .gap-6 {
  @apply gap-3;
}

.compact-layout .gap-4 {
  @apply gap-2;
}

/* Header Styles */
header {
  background: rgba(255, 255, 255, 0.9);
  border-bottom: 1px solid #e5e7eb;
  backdrop-filter: blur(8px);
}

/* Header Text and Icon Styles */
header .text-white,
header span.text-white,
header .font-bold.text-white {
  color: #000000;
}

header .text-slate-300,
header .text-slate-400 {
  color: #6b7280;
}

header .hover\:text-white:hover {
  color: #000000;
}

/* Header Button Styles */
header button.text-slate-300,
header button .text-slate-300,
header .text-slate-300 {
  color: #6b7280;
}

header button:hover,
header button.hover\:text-white:hover,
header .hover\:text-white:hover {
  color: #000000;
}

/* Header Logo Background Styles */
header .bg-blue-600,
header .bg-emerald-600,
header .bg-amber-600 {
  background: #000000;
}

header .bg-blue-600 span,
header .bg-emerald-600 span,
header .bg-amber-600 span {
  color: #ffffff;
}

/* Header Notification Badge Styles */
header .bg-red-500 {
  background: #dc2626;
}

/* Header Ghost Button Styles */
header button[variant="ghost"],
header .variant-ghost {
  color: #6b7280;
  background: transparent;
}

header button[variant="ghost"]:hover,
header .variant-ghost:hover {
  color: #000000;
  background: #f3f4f6;
}

/* Header Icon Colors */
header svg,
header .lucide {
  color: inherit;
}



/* Form Element Styles */
input,
textarea,
select,
.input,
[type="email"],
[type="password"],
[type="text"] {
  background: #ffffff;
  color: #000000;
  border: 1px solid #d1d5db;
  border-radius: 0.375rem;
}

input:focus,
textarea:focus,
select:focus {
  border-color: #000000;
  box-shadow: 0 0 0 1px #000000;
  outline: none;
}

/* Form Labels */
label,
.label {
  color: #000000;
  font-weight: 500;
}

/* Background Overrides */
.bg-slate-800,
.bg-slate-700,
.bg-slate-600,
.bg-gray-800,
.bg-gray-700 {
  background: #374151;
  color: #ffffff;
}

/* Form Placeholder Text */
input::placeholder,
textarea::placeholder {
  color: #9ca3af;
  opacity: 1;
}

/* Input Icons */
input + svg,
.input-container svg,
.relative svg {
  color: #6b7280;
  stroke: currentColor;
}

/* Button in Forms */
form button,
.form button {
  background: #000000;
  color: #ffffff;
  border: 1px solid #000000;
}

form button:hover,
.form button:hover {
  background: #374151;
  color: #ffffff;
}

/* Card Form Backgrounds */
.bg-slate-800\/50,
.bg-slate-900\/50,
.bg-gray-800\/50 {
  background: #ffffff;
  border: 1px solid #e5e7eb;
  box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1);
}

/* Border and Background Utilities */
.border-slate-700\/50 {
  border-color: #e5e7eb;
}

.bg-slate-900\/80 {
  background: rgba(255, 255, 255, 0.9);
}

/* Text Color Overrides */
.text-white {
  color: #000000;
}

.text-slate-300 {
  color: #6b7280;
}

.text-slate-400 {
  color: #9ca3af;
}

.text-slate-500 {
  color: #6b7280;
}

/* Button Color Overrides */
.bg-white {
  background: #000000;
  color: #ffffff;
}

.hover\:bg-slate-100:hover {
  background: #374151;
}

.text-slate-900 {
  color: #ffffff;
}

/* Card Styles */
.bg-gradient-to-br.from-slate-800\/50 {
  background: #ffffff;
  border: 1px solid #e5e7eb;
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
}

.border-slate-700\/30,
.border-slate-700\/50 {
  border-color: #e5e7eb;
}

/* Deal Pipeline Card Backgrounds */
.bg-slate-700\/30 {
  background: #ffffff;
  border: 1px solid #e5e7eb;
  box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);
}

/* Badge Styles - Pure B&W */
.bg-blue-500\/10,
.bg-emerald-500\/20 {
  background: #f3f4f6;
}

.text-blue-300,
.text-blue-400,
.text-blue-600,
.text-emerald-400,
.text-amber-400 {
  color: #000000;
}

.border-blue-500\/20 {
  border-color: #d1d5db;
}

/* Gradient Text Override - Pure B&W */
.bg-clip-text.text-transparent {
  color: #000000;
  background: none;
  -webkit-background-clip: unset;
  background-clip: unset;
}

/* Button Variants - Pure B&W */
.bg-blue-600,
.bg-amber-600,
.bg-emerald-600 {
  background: #000000;
  color: #ffffff;
}

.hover\:bg-blue-700:hover {
  background: #374151;
  color: #ffffff;
}

/* Logo and Brand Colors - Pure B&W */
.w-8.h-8.bg-blue-600 {
  background: #000000;
}

.border-white\/30 {
  border-color: #d1d5db;
}

.hover\:bg-white\/10:hover {
  background: #f3f4f6;
  color: #000000;
}

/* Comprehensive Color Overrides - Pure Black & White */

/* All Slate Text Colors to Grayscale */
.text-slate-100,
.text-slate-200,
.text-slate-700,
.text-slate-800,
.text-slate-900 { color: #000000; }

.text-slate-300 { color: #6b7280; }
.text-slate-400 { color: #9ca3af; }
.text-slate-500 { color: #6b7280; }
.text-slate-600 { color: #374151; }

/* All Background Slate Colors */
.bg-slate-50 { background: #ffffff; }
.bg-slate-100 { background: #f9fafb; }
.bg-slate-200 { background: #f3f4f6; }
.bg-slate-300 { background: #e5e7eb; }
.bg-slate-400 { background: #d1d5db; }
.bg-slate-500 { background: #9ca3af; }
.bg-slate-600 { background: #6b7280; }
.bg-slate-700 { background: #374151; }
.bg-slate-800 { background: #1f2937; }
.bg-slate-900 { background: #000000; }

/* All Border Slate Colors */
.border-slate-100 { border-color: #f3f4f6; }
.border-slate-200 { border-color: #e5e7eb; }
.border-slate-300 { border-color: #d1d5db; }
.border-slate-400 { border-color: #9ca3af; }
.border-slate-500 { border-color: #6b7280; }
.border-slate-600 { border-color: #374151; }
.border-slate-700 { border-color: #1f2937; }
.border-slate-800 { border-color: #000000; }
.border-slate-900 { border-color: #000000; }

/* All Blue Colors to Black/White */
.text-blue-50,
.text-blue-100,
.text-blue-200,
.text-blue-300,
.text-blue-400,
.text-blue-500,
.text-blue-600,
.text-blue-700,
.text-blue-800,
.text-blue-900 { color: #000000; }

.bg-blue-50 { background: #f9fafb; }
.bg-blue-100 { background: #f3f4f6; }
.bg-blue-200 { background: #e5e7eb; }
.bg-blue-300 { background: #d1d5db; }
.bg-blue-400 { background: #9ca3af; }
.bg-blue-500 { background: #6b7280; }
.bg-blue-600,
.bg-blue-700,
.bg-blue-800,
.bg-blue-900 { background: #000000; color: #ffffff; }

.border-blue-100 { border-color: #f3f4f6; }
.border-blue-200 { border-color: #e5e7eb; }
.border-blue-300 { border-color: #d1d5db; }
.border-blue-400 { border-color: #9ca3af; }
.border-blue-500 { border-color: #6b7280; }
.border-blue-600,
.border-blue-700,
.border-blue-800,
.border-blue-900 { border-color: #000000; }

/* All Emerald/Green Colors to Black/White */
.text-emerald-50,
.text-emerald-100,
.text-emerald-200,
.text-emerald-300,
.text-emerald-400,
.text-emerald-500,
.text-emerald-600,
.text-emerald-700,
.text-emerald-800,
.text-emerald-900 { color: #000000; }

.bg-emerald-50 { background: #f9fafb; }
.bg-emerald-100 { background: #f3f4f6; }
.bg-emerald-200 { background: #e5e7eb; }
.bg-emerald-300 { background: #d1d5db; }
.bg-emerald-400 { background: #9ca3af; }
.bg-emerald-500 { background: #6b7280; }
.bg-emerald-600,
.bg-emerald-700,
.bg-emerald-800,
.bg-emerald-900 { background: #000000; color: #ffffff; }

.border-emerald-100 { border-color: #f3f4f6; }
.border-emerald-200 { border-color: #e5e7eb; }
.border-emerald-300 { border-color: #d1d5db; }
.border-emerald-400 { border-color: #9ca3af; }
.border-emerald-500 { border-color: #6b7280; }
.border-emerald-600,
.border-emerald-700,
.border-emerald-800,
.border-emerald-900 { border-color: #000000; }

/* All Amber/Yellow Colors to Black/White */
.text-amber-50,
.text-amber-100,
.text-amber-200,
.text-amber-300,
.text-amber-400,
.text-amber-500,
.text-amber-600,
.text-amber-700,
.text-amber-800,
.text-amber-900 { color: #000000; }

.bg-amber-50 { background: #f9fafb; }
.bg-amber-100 { background: #f3f4f6; }
.bg-amber-200 { background: #e5e7eb; }
.bg-amber-300 { background: #d1d5db; }
.bg-amber-400 { background: #9ca3af; }
.bg-amber-500 { background: #6b7280; }
.bg-amber-600,
.bg-amber-700,
.bg-amber-800,
.bg-amber-900 { background: #000000; color: #ffffff; }

.border-amber-100 { border-color: #f3f4f6; }
.border-amber-200 { border-color: #e5e7eb; }
.border-amber-300 { border-color: #d1d5db; }
.border-amber-400 { border-color: #9ca3af; }
.border-amber-500 { border-color: #6b7280; }
.border-amber-600,
.border-amber-700,
.border-amber-800,
.border-amber-900 { border-color: #000000; }

/* Hover States */
.hover\:text-white:hover { color: #000000; }
.hover\:text-slate-300:hover { color: #6b7280; }
.hover\:bg-slate-100:hover { background: #f3f4f6; }
.hover\:bg-slate-200:hover { background: #e5e7eb; }
.hover\:bg-blue-700:hover,
.hover\:bg-emerald-700:hover,
.hover\:bg-amber-700:hover { background: #374151; color: #ffffff; }

/* Focus States */
.focus\:ring-blue-500:focus,
.focus\:ring-emerald-500:focus,
.focus\:ring-amber-500:focus { --tw-ring-color: #000000; }

/* Active States */
.active\:bg-blue-700:active,
.active\:bg-emerald-700:active,
.active\:bg-amber-700:active { background: #374151; }

/* Opacity Variants */
.bg-blue-600\/20,
.bg-emerald-500\/20,
.bg-amber-500\/20 { background: rgba(243, 244, 246, 0.2); }
.bg-slate-800\/50,
.bg-slate-900\/50 { background: rgba(255, 255, 255, 0.5); }

/* Button Text Fixes - Ensure White Text on Black Buttons */
button.bg-blue-600,
.bg-blue-600 button,
button.bg-emerald-600,
.bg-emerald-600 button,
button.bg-amber-600,
.bg-amber-600 button,
button.bg-black,
.bg-black button {
  background: #000000;
  color: #ffffff;
}

/* Button Hover Text Fixes */
button.bg-blue-600:hover,
.bg-blue-600 button:hover,
button.bg-emerald-600:hover,
.bg-emerald-600 button:hover,
button.bg-amber-600:hover,
.bg-amber-600 button:hover,
button.bg-black:hover,
.bg-black button:hover {
  background: #374151;
  color: #ffffff;
}

/* All Button Text Overrides */
button[class*="bg-blue"],
button[class*="bg-emerald"],
button[class*="bg-amber"],
button[class*="bg-black"],
.bg-blue-600 *,
.bg-emerald-600 *,
.bg-amber-600 *,
.bg-black * {
  color: #ffffff;
}

/* Button Component Overrides */
[role="button"][class*="bg-blue"],
[role="button"][class*="bg-emerald"],
[role="button"][class*="bg-amber"],
[role="button"][class*="bg-black"] {
  background: #000000;
  color: #ffffff;
}

/* Link Button Overrides */
a[class*="bg-blue"],
a[class*="bg-emerald"],
a[class*="bg-amber"],
a[class*="bg-black"] {
  background: #000000;
  color: #ffffff;
}

/* Force White Text on Dark Buttons Only */
button.bg-black,
button.bg-gray-900,
[role="button"].bg-black,
[role="button"].bg-gray-900,
.bg-black button,
.bg-gray-900 button {
  background: #000000;
  color: #ffffff;
}

/* Force White Text on Dark Button Children */
button.bg-black *,
button.bg-gray-900 *,
[role="button"].bg-black *,
[role="button"].bg-gray-900 *,
.bg-black button *,
.bg-gray-900 button * {
  color: #ffffff;
}

/* Override Any Remaining Text Colors in Buttons */
button span,
button div,
button p,
[role="button"] span,
[role="button"] div,
[role="button"] p,
.bg-blue-600 span,
.bg-blue-600 div,
.bg-emerald-600 span,
.bg-emerald-600 div,
.bg-amber-600 span,
.bg-amber-600 div,
.bg-black span,
.bg-black div {
  color: #ffffff;
}

/* Override Text Color Classes in Dark Buttons Only */
button.bg-black .text-slate-900,
button.bg-black .text-black,
button.bg-black .text-gray-900,
button.bg-gray-900 .text-slate-900,
button.bg-gray-900 .text-black,
button.bg-gray-900 .text-gray-900 {
  color: #ffffff;
}

/* Specific Button Text Overrides - Target All Button Scenarios */
button.bg-white,
.bg-white button,
a.bg-white,
.bg-white a {
  background: #000000;
  color: #ffffff;
}

/* Override White Background Buttons */
.bg-white.text-slate-900,
.bg-white .text-slate-900,
button.bg-white.text-slate-900,
.bg-white button.text-slate-900 {
  background: #000000;
  color: #ffffff;
}

/* Force All Button Text to White - Most Specific */
button[class*="bg-"],
[role="button"][class*="bg-"],
a[class*="bg-"] {
  color: #ffffff;
}

/* Override Any Text Color Classes in Buttons */
button .text-slate-900,
button .text-black,
button .text-gray-900,
button .text-white,
[role="button"] .text-slate-900,
[role="button"] .text-black,
[role="button"] .text-gray-900,
[role="button"] .text-white,
a .text-slate-900,
a .text-black,
a .text-gray-900,
a .text-white {
  color: #ffffff;
}

/* Ultra-Specific Button Text Fixes - Only Target Dark Buttons */
button.bg-black,
button.bg-black *,
button.bg-gray-900,
button.bg-gray-900 *,
[role="button"].bg-black,
[role="button"].bg-black *,
[role="button"].bg-gray-900,
[role="button"].bg-gray-900 *,
a.bg-black,
a.bg-black *,
a.bg-gray-900,
a.bg-gray-900 * {
  color: #ffffff;
}

/* Navigation Button Specific - Only Dark Buttons */
nav button.bg-black,
nav button.bg-black *,
nav button.bg-gray-900,
nav button.bg-gray-900 *,
header button.bg-black,
header button.bg-black *,
header button.bg-gray-900,
header button.bg-gray-900 * {
  color: #ffffff;
}

/* Override Text Colors Only in Buttons */
button .text-slate-900,
button .text-gray-900,
button .text-black,
[role="button"] .text-slate-900,
[role="button"] .text-gray-900,
[role="button"] .text-black,
a.bg-white .text-slate-900,
a.bg-blue-600 .text-white,
a.bg-emerald-600 .text-white,
a.bg-amber-600 .text-white {
  color: #ffffff;
}

/* Fix for MainNavigation Default Button - bg-white text-slate-900 */
.bg-white.text-slate-900,
button.bg-white.text-slate-900,
a.bg-white.text-slate-900,
[class*="bg-white"][class*="text-slate-900"],
button[class*="bg-white"][class*="text-slate-900"],
a[class*="bg-white"][class*="text-slate-900"] {
  background: #000000;
  color: #ffffff;
}

/* Fix for MainNavigation Hover States */
.bg-white.text-slate-900.hover\:bg-slate-100:hover,
button.bg-white.text-slate-900.hover\:bg-slate-100:hover,
a.bg-white.text-slate-900.hover\:bg-slate-100:hover {
  background: #374151;
  color: #ffffff;
}

/* Force Override Any White Background - Only for Buttons */
button.bg-white,
a.bg-white,
[role="button"].bg-white {
  background: #000000;
  color: #ffffff;
}

button.hover\:bg-slate-100:hover,
a.hover\:bg-slate-100:hover,
[role="button"].hover\:bg-slate-100:hover {
  background: #374151;
  color: #ffffff;
}

/* Additional Color Overrides */
.text-purple-400,
.text-red-400,
.text-yellow-400 { color: #000000; }

.bg-purple-500\/20,
.bg-red-500\/20,
.bg-yellow-500\/20 { background: #f3f4f6; }

/* Gradient Overrides */
.bg-gradient-to-r,
.from-blue-600,
.to-emerald-600 { background: #ffffff; }

/* Card Hover States */
.hover\:border-blue-500\/50:hover,
.hover\:border-amber-500\/50:hover,
.border-emerald-500\/50 { border-color: #d1d5db; }

/* Background Sections */
.bg-slate-800\/30,
.bg-slate-800\/50 {
  background: #ffffff;
  border: 1px solid #e5e7eb;
  box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);
}

/* Footer */
footer.bg-slate-900 { background: #f3f4f6; }
.border-slate-800 { border-color: #e5e7eb; }

/* Outline Button Fixes - Ensure Proper Contrast */
button[variant="outline"],
.variant-outline,
button.border,
a.border {
  background: transparent;
  color: #000000;
  border-color: #000000;
}

button[variant="outline"]:hover,
.variant-outline:hover,
button.border:hover,
a.border:hover {
  background: #000000;
  color: #ffffff;
  border-color: #000000;
}

/* Ensure Cards and Other Elements Don't Get Button Styling */
.bg-white:not(button):not([role="button"]):not(a) {
  background: #ffffff;
  color: #000000;
}

/* Icon Background Color Overrides - Remove All Colors */
.bg-blue-500\/20,
.bg-blue-600\/20,
.bg-emerald-500\/20,
.bg-emerald-600\/20,
.bg-amber-500\/20,
.bg-amber-600\/20,
.bg-purple-500\/20,
.bg-purple-600\/20,
.bg-red-500\/20,
.bg-red-600\/20,
.bg-yellow-500\/20,
.bg-yellow-600\/20,
.bg-green-500\/20,
.bg-green-600\/20,
.bg-indigo-500\/20,
.bg-indigo-600\/20,
.bg-pink-500\/20,
.bg-pink-600\/20,
.bg-teal-500\/20,
.bg-teal-600\/20 {
  background: #f3f4f6;
}

/* Icon Container Backgrounds - All Solid Colors to Gray */
.bg-blue-50,
.bg-blue-100,
.bg-emerald-50,
.bg-emerald-100,
.bg-amber-50,
.bg-amber-100,
.bg-purple-50,
.bg-purple-100,
.bg-red-50,
.bg-red-100,
.bg-yellow-50,
.bg-yellow-100,
.bg-green-50,
.bg-green-100,
.bg-indigo-50,
.bg-indigo-100,
.bg-pink-50,
.bg-pink-100,
.bg-teal-50,
.bg-teal-100 {
  background: #f9fafb;
}

/* Icon Text Colors - All Colors to Black */
.text-blue-400,
.text-blue-500,
.text-blue-600,
.text-emerald-400,
.text-emerald-500,
.text-emerald-600,
.text-amber-400,
.text-amber-500,
.text-amber-600,
.text-purple-400,
.text-purple-500,
.text-purple-600,
.text-red-400,
.text-red-500,
.text-red-600,
.text-yellow-400,
.text-yellow-500,
.text-yellow-600,
.text-green-400,
.text-green-500,
.text-green-600,
.text-indigo-400,
.text-indigo-500,
.text-indigo-600,
.text-pink-400,
.text-pink-500,
.text-pink-600,
.text-teal-400,
.text-teal-500,
.text-teal-600 {
  color: #000000;
}

/* Icon SVG Elements - Force Black Color */
svg.text-blue-400,
svg.text-emerald-400,
svg.text-amber-400,
svg.text-purple-400,
svg.text-red-400,
svg.text-yellow-400,
.text-blue-400 svg,
.text-emerald-400 svg,
.text-amber-400 svg,
.text-purple-400 svg,
.text-red-400 svg,
.text-yellow-400 svg {
  color: #000000;
  fill: currentColor;
}

/* Progress Bar Overrides - Remove Blue Colors */
.bg-blue-600,
.bg-blue-500,
.bg-blue-400,
[role="progressbar"],
.progress-bar,
.w-full.bg-blue-600,
.h-2.bg-blue-600 {
  background: #000000;
}

/* Progress Container */
.bg-slate-200,
.bg-gray-200,
.progress-container {
  background: #e5e7eb;
}

/* Purple Button Override */
.bg-purple-600,
.bg-purple-500,
button.bg-purple-600,
.bg-purple-600.text-white {
  background: #000000;
  color: #ffffff;
}

/* Green Background Override */
.bg-green-50,
.bg-green-100 {
  background: #f9fafb;
}

/* Action Item Backgrounds */
.bg-red-50,
.bg-orange-50,
.bg-yellow-50 {
  background: #f9fafb;
}

/* Menu Icon Fixes - Ensure Visibility (but respect text color classes) */
.lucide,
svg,
.icon {
  color: #000000;
  stroke: currentColor;
  fill: none;
  opacity: 1;
  visibility: visible;
  display: inline;
}

/* Dashboard Card Icons (not in header buttons) */
.w-4.h-4:not(header button .w-4.h-4),
.w-5.h-5:not(header button .w-5.h-5),
.w-6.h-6:not(header button .w-6.h-6) {
  color: #000000;
  stroke: #000000;
  fill: none;
  opacity: 1;
}

/* Button Icons - Only for Dark Buttons */
button.bg-black svg,
button.bg-gray-900 svg,
button.bg-black .lucide,
button.bg-gray-900 .lucide {
  color: #ffffff;
  stroke: #ffffff;
  fill: none;
  opacity: 1;
}

/* Default Button Icons */
button:not(.bg-black):not(.bg-gray-900) svg,
button:not(.bg-black):not(.bg-gray-900) .lucide {
  color: inherit;
  stroke: currentColor;
  fill: none;
  opacity: 1;
}







/* Card Content Icons - Specific for dashboard cards */
.bg-slate-700\/30 svg,
.bg-slate-800\/50 svg,
.bg-slate-700\/30 .lucide,
.bg-slate-800\/50 .lucide {
  color: #000000;
  stroke: #000000;
  fill: none;
  opacity: 1;
  visibility: visible;
}

/* Deal Pipeline Card Hover States */
.hover\:border-emerald-500\/50:hover {
  border-color: #d1d5db;
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
}

/* Button Overrides for Cards */
.border-slate-600 {
  border-color: #d1d5db;
  background: #ffffff;
  color: #000000;
}

.hover\:bg-slate-700:hover {
  background: #f3f4f6;
  color: #000000;
}

/* Specific Icon Fixes - Force Visibility */
.lucide-eye,
.lucide-heart,
.lucide-message-circle,
.lucide-arrow-right,
.lucide-map-pin,
.lucide-dollar-sign,
.lucide-building,
.lucide-trending-up,
.lucide-search,
.lucide-filter {
  color: #000000;
  stroke: #000000;
  fill: none;
  opacity: 1;
  visibility: visible;
  display: inline-block;
  width: auto;
  height: auto;
}

/* Icon Container Fixes */
.w-12.h-12.bg-emerald-500\/20,
.w-12.h-12.bg-blue-500\/20,
.w-12.h-12.bg-amber-500\/20 {
  background: #f3f4f6;
  border: 1px solid #e5e7eb;
}

/* Text Color Fixes - Comprehensive */
h1,
h2,
h3,
h4,
h5,
h6 {
  color: #000000;
}

/* Logo and Brand Text */
.text-2xl.font-bold,
.text-xl.font-bold,
.font-bold {
  color: #000000;
}

/* Background Overrides for Pages */
.bg-slate-900 {
  background: #ffffff;
}

.min-h-screen.bg-slate-900 {
  background: #f9fafb;
}

/* Container and Layout Fixes */
.container {
  background: transparent;
}

/* Deal Detail Page Specific Fixes */
.bg-slate-800\/50.border-slate-700\/50 {
  background: #ffffff;
  border: 1px solid #e5e7eb;
  box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);
}

/* Badge Status Colors */
.bg-emerald-500\/20.text-emerald-400.border-emerald-500\/30 {
  background: #dcfce7;
  color: #166534;
  border-color: #bbf7d0;
}

.bg-blue-500\/20.text-blue-400.border-blue-500\/30 {
  background: #dbeafe;
  color: #1e40af;
  border-color: #bfdbfe;
}

.bg-red-500\/20.text-red-400.border-red-500\/30 {
  background: #fee2e2;
  color: #dc2626;
  border-color: #fecaca;
}

/* Auth Page Fixes */
.bg-slate-800\/50.border-slate-700\/50.backdrop-blur-sm {
  background: rgba(255, 255, 255, 0.95);
  border: 1px solid #e5e7eb;
}

/* "OR CONTINUE WITH" Text Fix */
.bg-slate-800.px-2.text-slate-400 {
  background: #ffffff;
  color: #6b7280;
}

/* Input Field Fixes */
.bg-slate-700\/50.border-slate-600 {
  background: #ffffff;
  border: 1px solid #d1d5db;
  color: #000000;
}

.bg-slate-700\/50.border-slate-600::placeholder {
  color: #9ca3af;
}

/* Deal Detail Page Background Fix */
.min-h-screen.bg-gradient-to-br.from-slate-900.via-slate-800.to-slate-900 {
  background: linear-gradient(135deg, #f8fafc 0%, #ffffff 100%);
}

/* Deal Detail Button Fixes */
.border-slate-600.text-white {
  border-color: #d1d5db;
  color: #000000;
  background: #ffffff;
}

.border-slate-600.text-white:hover {
  background: #f3f4f6;
  color: #000000;
}

/* Deal Detail Badge Fixes */
.border-slate-600.text-slate-400 {
  border-color: #d1d5db;
  color: #6b7280;
  background: #f9fafb;
}

/* Consultant/Discovery Call Page Fixes */
.bg-slate-800\/50.border-slate-700\/50.backdrop-blur-sm {
  background: rgba(255, 255, 255, 0.95);
  border: 1px solid #e5e7eb;
  backdrop-filter: blur(8px);
}

/* Card Content and Text Fixes */
.text-slate-200 {
  color: #374151;
}

/* Icon Background Fixes */
.w-12.h-12.bg-blue-500\/20,
.w-12.h-12.bg-emerald-500\/20,
.w-12.h-12.bg-amber-500\/20,
.w-12.h-12.bg-purple-500\/20,
.w-12.h-12.bg-red-500\/20 {
  background: #f3f4f6;
  border: 1px solid #e5e7eb;
}

/* Icon Colors */
.w-12.h-12 svg,
.w-12.h-12 .lucide {
  color: #374151;
  stroke: #374151;
}

/* Avatar/Profile Icon Fixes */
.w-10.h-10.bg-slate-600,
.w-12.h-12.bg-slate-600 {
  background: #e5e7eb;
  color: #374151;
}

/* Rating Stars */
.text-yellow-400 {
  color: #f59e0b;
}

/* Badge Fixes for Consultant Cards */
.bg-blue-500\/10.text-blue-400,
.bg-emerald-500\/10.text-emerald-400,
.bg-purple-500\/10.text-purple-400 {
  background: #f3f4f6;
  color: #374151;
  border: 1px solid #d1d5db;
}

/* Advisor Card Fixes */
.bg-slate-700 {
  background: #f3f4f6;
  color: #374151;
}

/* Calendar Icon and Date Picker Fixes */
.w-4.h-4.text-slate-400,
.w-5.h-5.text-slate-400 {
  color: #6b7280;
}

/* Input Field Background */
input[type="date"],
input[type="time"],
input {
  background: #ffffff;
  border: 1px solid #d1d5db;
  color: #000000;
}

/* Select Dropdown */
select {
  background: #ffffff;
  border: 1px solid #d1d5db;
  color: #000000;
}

/* Upcoming Calls Card */
.bg-slate-700\/50 {
  background: #f9fafb;
  border: 1px solid #e5e7eb;
}

/* Call Card Icons */
.w-4.h-4.text-slate-500,
.w-5.h-5.text-slate-500 {
  color: #6b7280;
}

/* Why Schedule Call Section - Black and White Only */
.w-6.h-6.text-emerald-400 {
  color: #000000;
}

/* Border Colors - Black and White Only */
.border-blue-500\/30,
.border-emerald-500\/30,
.border-amber-500\/30,
.border-purple-500\/30,
.border-red-500\/30 {
  border-color: #d1d5db;
}

/* Compact Design System - Reduced Spacing Utilities */
.compact-container {
  @apply px-3 py-4;
}

.compact-section {
  @apply mb-4;
}

.compact-card {
  @apply p-3;
}

.compact-card-header {
  @apply p-3 pb-2;
}

.compact-card-content {
  @apply p-3 pt-0;
}

.compact-grid-gap {
  @apply gap-3;
}

.compact-button {
  @apply h-8 px-3 text-sm;
}

.compact-input {
  @apply h-8 px-2 text-sm;
}

.compact-badge {
  @apply px-2 py-0.5 text-xs;
}

.compact-icon {
  @apply w-4 h-4;
}

.compact-icon-lg {
  @apply w-5 h-5;
}

.compact-spacing-xs {
  @apply space-y-1;
}

.compact-spacing-sm {
  @apply space-y-2;
}

.compact-spacing-md {
  @apply space-y-3;
}

.compact-text-xs {
  @apply text-xs leading-tight;
}

.compact-text-sm {
  @apply text-sm leading-tight;
}

.compact-text-base {
  @apply text-base leading-tight;
}

/* Override default spacing for compact design */
.compact-layout .mb-8 {
  @apply mb-4;
}

.compact-layout .py-8 {
  @apply py-4;
}

.compact-layout .p-6 {
  @apply p-3;
}

.compact-layout .gap-6 {
  @apply gap-3;
}

.compact-layout .space-y-4 > * + * {
  @apply mt-2;
}

.compact-layout .space-y-6 > * + * {
  @apply mt-3;
}

/* Compact Dashboard Specific */
.compact-dashboard-header {
  @apply mb-4 py-3;
}

.compact-dashboard-metrics {
  @apply grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-3 mb-4;
}

.compact-dashboard-card {
  @apply bg-white border border-gray-200 rounded-lg p-3 shadow-sm;
}

.compact-dashboard-card-icon {
  @apply w-8 h-8 bg-gray-100 rounded-md flex items-center justify-center mb-2;
}

.compact-dashboard-metric {
  @apply text-xl font-bold text-gray-900 mb-1;
}

.compact-dashboard-label {
  @apply text-xs text-gray-600;
}

@layer base {
  * {
    @apply border-border outline-ring/50;
  }
  body {
    @apply bg-background text-foreground;
    transition: background-color 0.3s ease, color 0.3s ease;
  }
}
