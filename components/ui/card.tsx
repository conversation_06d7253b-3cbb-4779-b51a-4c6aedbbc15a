import { cva, type VariantProps } from "class-variance-authority"
import * as React from "react"

import { cn } from "@/lib/utils"

const cardVariants = cva(
  "bg-card text-card-foreground flex flex-col rounded-xl border shadow-sm",
  {
    variants: {
      variant: {
        default: "gap-6 py-6",
        compact: "gap-3 py-3",
      },
    },
    defaultVariants: {
      variant: "default",
    },
  }
)

function Card({
  className,
  variant,
  ...props
}: React.ComponentProps<"div"> & VariantProps<typeof cardVariants>) {
  return (
    <div
      data-slot="card"
      className={cn(cardVariants({ variant, className }))}
      {...props}
    />
  )
}

const cardHeaderVariants = cva(
  "@container/card-header grid auto-rows-min grid-rows-[auto_auto] items-start gap-1.5 has-data-[slot=card-action]:grid-cols-[1fr_auto]",
  {
    variants: {
      variant: {
        default: "px-6 [.border-b]:pb-6",
        compact: "px-3 [.border-b]:pb-3",
      },
    },
    defaultVariants: {
      variant: "default",
    },
  }
)

function CardHeader({
  className,
  variant,
  ...props
}: React.ComponentProps<"div"> & VariantProps<typeof cardHeaderVariants>) {
  return (
    <div
      data-slot="card-header"
      className={cn(cardHeaderVariants({ variant, className }))}
      {...props}
    />
  )
}

function CardTitle({ className, ...props }: React.ComponentProps<"div">) {
  return (
    <div
      data-slot="card-title"
      className={cn("leading-none font-semibold", className)}
      {...props}
    />
  )
}

function CardDescription({ className, ...props }: React.ComponentProps<"div">) {
  return (
    <div
      data-slot="card-description"
      className={cn("text-muted-foreground text-sm", className)}
      {...props}
    />
  )
}

function CardAction({ className, ...props }: React.ComponentProps<"div">) {
  return (
    <div
      data-slot="card-action"
      className={cn(
        "col-start-2 row-span-2 row-start-1 self-start justify-self-end",
        className
      )}
      {...props}
    />
  )
}

const cardContentVariants = cva(
  "",
  {
    variants: {
      variant: {
        default: "px-6",
        compact: "px-3",
      },
    },
    defaultVariants: {
      variant: "default",
    },
  }
)

function CardContent({
  className,
  variant,
  ...props
}: React.ComponentProps<"div"> & VariantProps<typeof cardContentVariants>) {
  return (
    <div
      data-slot="card-content"
      className={cn(cardContentVariants({ variant, className }))}
      {...props}
    />
  )
}

const cardFooterVariants = cva(
  "flex items-center",
  {
    variants: {
      variant: {
        default: "px-6 [.border-t]:pt-6",
        compact: "px-3 [.border-t]:pt-3",
      },
    },
    defaultVariants: {
      variant: "default",
    },
  }
)

function CardFooter({
  className,
  variant,
  ...props
}: React.ComponentProps<"div"> & VariantProps<typeof cardFooterVariants>) {
  return (
    <div
      data-slot="card-footer"
      className={cn(cardFooterVariants({ variant, className }))}
      {...props}
    />
  )
}

export {
    Card, CardAction, CardContent, CardDescription, CardFooter, CardHeader, CardTitle
}

