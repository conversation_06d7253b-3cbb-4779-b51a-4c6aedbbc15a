import Image from "next/image";
import Link from "next/link";

interface LogoProps {
  href?: string;
  size?: "sm" | "md" | "lg" | "xl";
  showText?: boolean;
  className?: string;
}

export default function Logo({
  href = "/",
  size = "md",
  showText = true,
  className = ""
}: LogoProps) {
  // Logo aspect ratio is ~4:1 (1146x290), so we use proper proportions
  const sizeClasses = {
    sm: "w-20 h-5",    // 80x20px - mobile/compact
    md: "w-32 h-8",    // 128x32px - standard header (close to 120x30px)
    lg: "w-40 h-10",   // 160x40px - large header
    xl: "w-52 h-13"    // 208x52px - hero sections (close to 200x50px)
  };

  const textSizeClasses = {
    sm: "text-lg",
    md: "text-xl",
    lg: "text-2xl",
    xl: "text-3xl"
  };

  // Maintain 4:1 aspect ratio for all sizes
  const logoSizes = {
    sm: { width: 80, height: 20 },   // w-20 h-5
    md: { width: 128, height: 32 },  // w-32 h-8
    lg: { width: 160, height: 40 },  // w-40 h-10
    xl: { width: 208, height: 52 }   // w-52 h-13 (closest to 4:1 ratio)
  };

  const logoContent = (
    <div className={`flex items-center space-x-3 ${className}`}>
      <div className={`${sizeClasses[size]} relative`}>
        <Image
          src="/tenxcfo.png"
          alt="TenxCFO Logo"
          width={logoSizes[size].width}
          height={logoSizes[size].height}
          className="object-contain"
          priority
        />
      </div>
      {showText && (
        <span className={`${textSizeClasses[size]} font-bold text-black`}>
          TenxCFO
        </span>
      )}
    </div>
  );

  if (href) {
    return (
      <Link href={href} className="flex items-center">
        {logoContent}
      </Link>
    );
  }

  return logoContent;
}
