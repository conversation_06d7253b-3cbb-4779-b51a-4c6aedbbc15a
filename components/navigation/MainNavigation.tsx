"use client";

import { But<PERSON> } from "@/components/ui/button";
import Link from "next/link";
import { usePathname } from "next/navigation";

interface NavigationProps {
  variant?: "default" | "sme" | "investor" | "consultant";
}

export default function MainNavigation({ variant = "default" }: NavigationProps) {
  const pathname = usePathname();

  const isActive = (path: string) => {
    if (path === "/") return pathname === "/";
    return pathname.startsWith(path);
  };

  const getActiveClass = (path: string) => {
    return isActive(path) 
      ? "text-white font-semibold border-b-2 border-blue-400" 
      : "text-slate-300 hover:text-white transition-colors";
  };

  const getButtonVariant = () => {
    switch (variant) {
      case "sme": return { bg: "bg-blue-600 hover:bg-blue-700", text: "Get Started" };
      case "investor": return { bg: "bg-emerald-600 hover:bg-emerald-700", text: "Access Deals" };
      case "consultant": return { bg: "bg-amber-600 hover:bg-amber-700", text: "Start Consulting" };
      default: return { bg: "bg-white text-slate-900 hover:bg-slate-100", text: "Sign up" };
    }
  };

  const buttonStyle = getButtonVariant();

  return (
    <nav className="hidden md:flex items-center space-x-8">
      <Link href="/sme" className={getActiveClass("/sme")}>
        For SMEs
      </Link>
      <Link href="/investor" className={getActiveClass("/investor")}>
        For Investors
      </Link>
      <Link href="/consultant" className={getActiveClass("/consultant")}>
        For Consultants
      </Link>
      <Link href="/10x-growth-hack" className={getActiveClass("/10x-growth-hack")}>
        10X Growth
      </Link>
      <Link href="/pricing" className={getActiveClass("/pricing")}>
        Pricing
      </Link>
      <Link href="/auth/signin" className={getActiveClass("/auth/signin")}>
        Log in
      </Link>
      
      {variant === "default" ? (
        <Link href="/auth/signup">
          <Button className={`${buttonStyle.bg} rounded-full px-6`}>
            {buttonStyle.text}
          </Button>
        </Link>
      ) : (
        <Button className={`${buttonStyle.bg} text-white rounded-full px-6`}>
          {buttonStyle.text}
        </Button>
      )}
    </nav>
  );
}
