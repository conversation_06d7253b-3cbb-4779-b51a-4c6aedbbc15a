"use client";

import { But<PERSON> } from "@/components/ui/button";
import Logo from "@/components/ui/logo";
import { useAuthStore } from "@/stores/authStore";
import { Bell, LogOut, Settings } from "lucide-react";
import Link from "next/link";
import { usePathname, useRouter } from "next/navigation";

interface AppHeaderProps {
  className?: string;
  variant?: "sme" | "investor" | "consultant" | "default";
}

export default function AppHeader({ className = "" }: AppHeaderProps) {
  console.log("AppHeader rendered");
  const pathname = usePathname();
  const router = useRouter();
  const { user, isAuthenticated, logout } = useAuthStore();
  console.log("user:", user);
  console.log("isAuthenticated:", isAuthenticated);

  const variant = isAuthenticated && user ? user.role : 'default';

  const handleLogout = () => {
    logout();
    router.push('/');
  };

  const isActive = (path: string) => {
    if (path === "/") return pathname === "/";
    return pathname.startsWith(path);
  };

  const getActiveClass = (path: string) => {
    const baseClass = "transition-colors";
    if (variant === "default") {
      return isActive(path) 
        ? `text-black font-semibold ${baseClass}` 
        : `text-gray-600 hover:text-black ${baseClass}`;
    } else {
      return isActive(path) 
        ? `text-white font-semibold border-b-2 ${getBorderColor()} ${baseClass}` 
        : `text-slate-300 hover:text-white ${baseClass}`;
    }
  };

  const getBorderColor = () => {
    switch (variant) {
      case "sme": return "border-blue-400";
      case "investor": return "border-emerald-400";
      case "consultant": return "border-amber-400";
      default: return "border-blue-400";
    }
  };

  const getButtonStyle = () => {
    switch (variant) {
      case "sme":
        return { bg: "bg-blue-600 hover:bg-blue-700", text: "Get Started" };
      case "investor":
        return { bg: "bg-emerald-600 hover:bg-emerald-700", text: "Start Investing" };
      case "consultant":
        return { bg: "bg-amber-600 hover:bg-amber-700", text: "Start Consulting" };
      default:
        return { bg: "bg-black text-white hover:bg-gray-800", text: "Sign up" };
    }
  };

  const getHeaderStyle = () => {
    if (variant === "default") {
      return "border-b border-gray-200 bg-white/90 backdrop-blur-sm sticky top-0 z-50";
    } else {
      return "border-b border-slate-700/50 bg-slate-900/80 backdrop-blur-sm sticky top-0 z-50";
    }
  };

  const getDashboardPath = () => {
    if (!user) return '/sme/dashboard';
    switch (user.role) {
      case 'investor': return '/investor/dashboard';
      case 'consultant': return '/consultant/dashboard';
      default: return '/sme/dashboard';
    }
  };

  const buttonStyle = getButtonStyle();

  if (isAuthenticated && user) {
    return (
      <header className="border-b border-slate-700/50 bg-slate-900/80 backdrop-blur-sm sticky top-0 z-50">
        <div className="container mx-auto px-4 py-4 flex items-center justify-between">
          <Logo href="/" size="md" showText={false} />
          <div className="flex items-center space-x-4">
            <Link href={getDashboardPath()}>
              <Button variant="ghost" size="sm" className="text-slate-300 hover:text-white">
                Dashboard
              </Button>
            </Link>
            <Link href="/notifications">
              <Button variant="ghost" size="sm" className="relative text-slate-300 hover:text-white">
                <Bell className="w-4 h-4" />
                <span className="absolute -top-1 -right-1 w-2 h-2 bg-red-500 rounded-full"></span>
              </Button>
            </Link>
            <Link href="/settings">
              <Button variant="ghost" size="sm" className="text-slate-300 hover:text-white">
                <Settings className="w-4 h-4" />
              </Button>
            </Link>
            <Button variant="ghost" size="sm" className="text-slate-300 hover:text-white" onClick={handleLogout}>
              <LogOut className="w-4 h-4" />
            </Button>
          </div>
        </div>
      </header>
    );
  }

  return (
    <header className={`${getHeaderStyle()} ${className}`}>
      <div className="container mx-auto px-4 py-4 flex items-center justify-between">
        <Logo href="/" size="md" showText={false} />
        
        <div className="flex items-center space-x-8">
          {/* Main Navigation */}
          {!isAuthenticated && (
            <nav className="hidden md:flex items-center space-x-8">
              <Link href="/sme" className={getActiveClass("/sme")}>
                For SMEs
              </Link>
              <Link href="/investor" className={getActiveClass("/investor")}>
                For Investors
              </Link>
              <Link href="/consultant" className={getActiveClass("/consultant")}>
                For Consultants
              </Link>
              <Link href="/10x-growth-hack" className={getActiveClass("/10x-growth-hack")}>
                10X Growth
              </Link>
              <Link href="/pricing" className={getActiveClass("/pricing")}>
                Pricing
              </Link>
            </nav>
          )}

          {/* Authentication-based Navigation */}
          {isAuthenticated && user ? (
            <div className="flex items-center space-x-4">
              {/* User Dashboard Link */}
              <Link href={getDashboardPath()}>
                <Button 
                  variant="ghost" 
                  size="sm" 
                  className={variant === "default" ? "text-gray-600 hover:text-black" : "text-slate-300 hover:text-white"}
                >
                  Dashboard
                </Button>
              </Link>

              {/* Notifications */}
              <Link href="/notifications">
                <Button 
                  variant="ghost" 
                  size="sm" 
                  className={`relative ${variant === "default" ? "text-gray-600 hover:text-black" : "text-slate-300 hover:text-white"}`}
                >
                  <Bell className="w-4 h-4 text-black" />
                  <span className="absolute -top-1 -right-1 w-2 h-2 bg-red-500 rounded-full"></span>
                </Button>
              </Link>

              {/* Settings */}
              <Link href="/settings">
                <Button 
                  variant="ghost" 
                  size="sm" 
                  className={variant === "default" ? "text-gray-600 hover:text-black" : "text-slate-300 hover:text-white"}
                >
                  <Settings className="w-4 h-4 text-black" />
                </Button>
              </Link>

              {/* Logout */}
              <Button 
                variant="ghost" 
                size="sm" 
                className={variant === "default" ? "text-gray-600 hover:text-black" : "text-slate-300 hover:text-white"}
                onClick={handleLogout}
              >
                <LogOut className="w-4 h-4 text-black" />
              </Button>
            </div>
          ) : (
            <div className="flex items-center space-x-4">
              <Link href="/auth/signin" className={getActiveClass("/auth/signin")}>
                Log in
              </Link>
              <Link href="/auth/signup">
                <Button className={`${buttonStyle.bg} rounded-full px-6`}>
                  {buttonStyle.text}
                </Button>
              </Link>
            </div>
          )}
        </div>
      </div>
    </header>
  );
}
