import type { NextConfig } from "next";

const nextConfig: NextConfig = {
  // Enable static export for deployment
  output: 'export',
  trailingSlash: true,
  skipTrailingSlashRedirect: true,

  // Handle dynamic routes for static export
  generateBuildId: async () => {
    return '10xcfo-build'
  },

  experimental: {
    optimizePackageImports: ['lucide-react', 'recharts'],
  },

  images: {
    formats: ['image/webp', 'image/avif'],
    // Disable image optimization for static export
    unoptimized: true,
  },

  // Enable strict mode for better development experience
  reactStrictMode: true,

  // Configure asset prefix for CloudFront (only in production)
  assetPrefix: process.env.NODE_ENV === 'production' ? 'https://d2tcjdwexogrvt.cloudfront.net' : '',

  // ESLint configuration for deployment
  eslint: {
    // Ignore ESLint errors during build for deployment
    ignoreDuringBuilds: true,
  },

  // TypeScript configuration for deployment
  typescript: {
    // Ignore TypeScript errors during build for deployment
    ignoreBuildErrors: true,
  },
};

export default nextConfig;
